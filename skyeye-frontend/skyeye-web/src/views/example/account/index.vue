<template>
    <div>
        <a-card title="SkAccount 组件示例">
            <!-- API说明 -->
            <div class="api-section">
                <h3>API 说明</h3>
                <a-table :columns="apiColumns" :data-source="apiData" :pagination="false" />
            </div>

            <!-- 示例展示 -->
            <div class="example-section">
                <h3>使用示例</h3>
                <a-tabs>
                    <!-- 基础用法 -->
                    <a-tab-pane key="basic" tab="基础用法">
                        <div class="example-content">
                            <div class="example-demo">
                                <SkAccountSelect v-model="basicAccount" placeholder="请选择账户" />
                                <div class="value-display">当前值: {{ basicAccount }}</div>
                            </div>
                            <a-card class="code-card">
                                <template #extra>
                                    <a-button type="link" @click="copyCode('basic')">
                                        <template #icon><copy-outlined /></template>
                                        复制代码
                                    </a-button>
                                </template>
                                <pre><code v-html="highlightCode(basicCode)" class="language-vue"></code></pre>
                            </a-card>
                        </div>
                    </a-tab-pane>

                    <!-- 查看模式 -->
                    <a-tab-pane key="view" tab="查看模式">
                        <div class="example-content">
                            <div class="example-demo">
                                <SkAccountSelect v-model="viewAccount" :isEdit="$config.formEditType.notEdit"
                                    :formData="formData" attrKey="account" />
                            </div>
                            <a-card class="code-card">
                                <template #extra>
                                    <a-button type="link" @click="copyCode('view')">
                                        <template #icon><copy-outlined /></template>
                                        复制代码
                                    </a-button>
                                </template>
                                <pre><code v-html="highlightCode(viewCode)" class="language-vue"></code></pre>
                            </a-card>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </a-card>
    </div>
</template>

<script setup>
import { ref, reactive ,getCurrentInstance} from 'vue'
import { message } from 'ant-design-vue'
import { CopyOutlined } from '@ant-design/icons-vue'
import SkAccountSelect from '@/components/SkAccountSelect/index.vue'
import hljs from 'highlight.js/lib/core'
import vue from 'highlight.js/lib/languages/xml'
import javascript from 'highlight.js/lib/languages/javascript'
import 'highlight.js/styles/atom-one-light.css'

// 注册语言
hljs.registerLanguage('vue', vue)
hljs.registerLanguage('javascript', javascript)

// 代码高亮处理函数
const highlightCode = (code) => {
    return hljs.highlight(code, { language: 'vue' }).value
}
const { proxy } = getCurrentInstance()

// API 表格配置
const apiColumns = [
    { title: '参数', dataIndex: 'param', key: 'param' },
    { title: '说明', dataIndex: 'desc', key: 'desc' },
    { title: '类型', dataIndex: 'type', key: 'type' },
    { title: '默认值', dataIndex: 'default', key: 'default' }
]

const apiData = [
    {
        param: 'modelValue',
        desc: '选择器的值',
        type: 'String/Number',
        default: '-'
    },
    {
        param: 'attrKey',
        desc: '属性键名',
        type: 'String',
        default: '-'
    },
    {
        param: 'isEdit',
        desc: '编辑模式',
        type: 'Number',
        default: '$config.formEditType.isEdit'
    },
    {
        param: 'formData',
        desc: '表单数据对象',
        type: 'Object',
        default: '{}'
    },
    {
        param: 'pageType',
        desc: '页面类型',
        type: 'String',
        default: ''
    }
]

// 示例数据
const basicAccount = ref('')
const viewAccount = ref('1')

const formData = reactive({
    account: '1',
    accountMation: {
        id: '1',
        name: '测试账户'
    }
})

// 示例代码
const basicCode =
    `// 引入组件
import SkAccountSelect from '@/components/SkAccountSelect/index.vue'

<template>
    <SkAccountSelect
        v-model="basicAccount"
        placeholder="请选择账户"
    />
</template>

<script setup>
import { ref } from 'vue'
const basicAccount = ref('')
\<\/script>`

const viewCode =
    `// 引入组件
import SkAccountSelect from '@/components/SkAccountSelect/index.vue'

<template>
    <SkAccountSelect
        v-model="viewAccount"
        :isEdit="$config.formEditType.notEdit"
        :formData="formData"
        attrKey="account"
    />
</template>

<script setup>
import { ref, reactive } from 'vue'
const viewAccount = ref('1')

const formData = reactive({
    account: '1',
    accountMation: {
        id: '1',
        name: '测试账户'
    }
})
\<\/script>`

// 复制代码功能
const copyCode = (type) => {
    let code = ''
    switch (type) {
        case 'basic':
            code = basicCode
            break
        case 'view':
            code = viewCode
            break
    }
    if (!code) {
        message.warning('没有可复制的内容')
        return
    }
    proxy.$util.copyToClipboard(code,{
        onSuccess: () => message.success('代码已复制到剪贴板'),
        onError: () => message.error('复制失败')
    }) 
}
</script>

<style scoped>
.api-section {
    margin-bottom: 32px;
}

.example-section {
    h3 {
        margin-bottom: 16px;
        color: #1f1f1f;
        font-weight: 500;
    }
}

.example-content {
    padding: 16px 0;
}

.example-demo {
    margin-bottom: 24px;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.value-display {
    margin-top: 8px;
    color: #666;
    font-size: 14px;
}

.code-card {
    background: #fafafa;
}

.code-card :deep(pre) {
    margin: 0;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    overflow-x: auto;
}

.code-card :deep(code) {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
}
</style>
